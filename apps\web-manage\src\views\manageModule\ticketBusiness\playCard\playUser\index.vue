<script setup lang="ts">
import { Page, useVbenModal } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { getUserCardList, getUserCardInfo, changeUserCardStatus } from '#/api/manageModule';
import { useColumns, useGridFormSchema } from './data';
import { Button, Modal, message } from 'ant-design-vue';
import Info from './modules/info.vue';

const showInfo = async (row: any) => {
  let info = await getUserCardInfo({ id: row.id });
  infoModelApi.setData(info).open();
};
const [InfoModel, infoModelApi] = useVbenModal({
  connectedComponent: Info,
  destroyOnClose: true,
});
const onStatusChange = async (row: any) => {
  let newStatus: number;
  if (row.cardStatus == 11) {
    newStatus = 9;
  } else {
    newStatus = 11;
  }
  const status: any = {
    9: '启用',
    11: '禁用',
  };
  try {
    await confirm(
      `你要将【${row.cardName}】的状态切换为 【${status[newStatus]}】 吗？`,
      `切换状态`,
    );
    await changeUserCardStatus({ id: row.id, status: newStatus });
    message.success('操作成功');
    onRefresh();
    return true;
  } catch {
    return false;
  }
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [
      ['orderDate', ['orderStartDate', 'orderEndDate']],
      ['activeDate', ['activeStartDate', 'activeEndDate']],
    ],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 1,
    wrapperClass: 'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: true,
  },

  gridOptions: {
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }: any, formValues: any) => {
          const res: any = await getUserCardList({
            page: page.currentPage,
            pageSize: page.pageSize,
            model: 1,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});

function onRefresh() {
  gridApi.query();
}
/**
 * 将Antd的Modal.confirm封装为promise，方便在异步函数中调用。
 * @param content 提示内容
 * @param title 提示标题
 */
const confirm = (content: string, title: string) => {
  return new Promise((reslove, reject) => {
    Modal.confirm({
      content,
      onCancel() {
        reject(new Error('已取消'));
      },
      onOk() {
        reslove(true);
      },
      title,
    });
  });
};
</script>
<template>
  <Page auto-content-height>
    <Grid>
      <template #isBindFace="{ row }">
        <div
          :style="row.isBindFace == 1 ? 'color:hsl(var(--success))' : ''"
          class="flex items-center justify-center"
        >
          <IconifyIcon icon="mdi:face-recognition" class="size-6"></IconifyIcon>
        </div>
      </template>
      <template #validDate="{ row }">
        <div>{{ row.validBeginDate + '~' + row.validEndDate }}</div>
      </template>
      <template #canVerificationNum="{ row }">
        <div>
          {{ row.canVerificationNum == -1 ? '不限' : row.canVerificationNum }}
        </div>
      </template>
      <template #verificationTotal="{ row }">
        <div>
          {{ row.verificationTotal == -1 ? '不限' : row.verificationTotal }}
        </div>
      </template>
      <template #action="{ row }">
        <Button
          type="link"
          size="small"
          v-if="row.cardStatus == 11"
          @click="onStatusChange(row)"
          >启用</Button
        >
        <Button
          type="link"
          danger
          size="small"
          v-if="row.cardStatus == 9"
          @click="onStatusChange(row)"
          >禁用</Button
        >
        <Button type="link" size="small" @click="showInfo(row)">详情</Button>
      </template>
    </Grid>
    <InfoModel></InfoModel>
  </Page>
</template>
