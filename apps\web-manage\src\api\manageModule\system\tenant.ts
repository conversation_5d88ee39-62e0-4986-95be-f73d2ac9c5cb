import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * 获取租户列表数据
 */
async function getTenantList(params: Recordable<any>) {
  return requestClient.get('/plat/tenant/list', {
    params,
  });
}

/**
 * 获取所有租户列表数据
 */
async function getAllTenantList(params: Recordable<any>) {
  return requestClient.get('/sys/adminTenant/all', {
    params,
  });
}

/**
 * 获取租户权限数据
 */
async function getTenantPermissions(id: string) {
  return requestClient.get('/plat/tenant/permissions', { params: { id } });
}

/**
 * 创建租户
 * @param data 租户数据
 */
async function createTenant(data: Recordable<any>) {
  return requestClient.post('/plat/tenant/create', data);
}
/**
 * 更新租户权限
 * @param data 租户权限数据
 */
async function updateTenantPermissions(data: Recordable<any>) {
  return requestClient.post('/plat/tenant/permissions', data);
}

/**
 * 更新租户
 *
 * @param id 租户 ID
 * @param data 租户数据
 */
async function updateTenant(data: Recordable<any>) {
  return requestClient.post('/plat/tenant/update', data);
}

/**
 * 更新租户
 *
 * @param id 租户 ID
 * @param data 租户数据
 */
async function changeTenantStatus(data: Recordable<any>) {
  return requestClient.post('/plat/tenant/changeStatus', data);
}

/**
 * 删除租户
 * @param id 租户 ID
 */
async function deleteTenant(id: string) {
  return requestClient.post('/plat/tenant/delete', { id });
}

/**
 * 初始化租户
 * @param params 查询参数
 */

async function initTenant(id: string) {
  return requestClient.post('/plat/tenant/initTenant', { id });
}

export {
  createTenant,
  deleteTenant,
  getTenantList,
  updateTenant,
  changeTenantStatus,
  getAllTenantList,
  getTenantPermissions,
  updateTenantPermissions,
  initTenant,
};
