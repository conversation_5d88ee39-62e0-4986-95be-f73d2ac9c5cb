<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Page, useVbenModal } from '@vben/common-ui';
import { Plus } from '@vben/icons';
import { Button, message, Select } from 'ant-design-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import {
  getTicketTypeList,
  getTicketTypeInfo,
  deleteTicketType,
  getAllScenicList,
} from '#/api/manageModule';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';

const [FormModel, formModelApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});

const onCreate = () => {
  formModelApi.setData({}).open();
};
const onEdit = async (row: any) => {
  const res = await getTicketTypeInfo(row.id);
  formModelApi.setData(res).open();
};

const onDelete = async (row: any) => {
  const hideLoading = message.loading({
    content: '删除中...',
    duration: 0,
    key: 'action_process_msg',
  });
  deleteTicketType(row.id)
    .then(() => {
      message.success('删除成功');
      onRefresh();
      hideLoading();
    })
    .catch(() => {
      hideLoading();
    });
};
const onRefresh = () => {
  gridApi.query();
};

const onActionClick = ({ code, row }: { code: string; row: any }) => {
  if (code === 'edit') {
    onEdit(row);
  } else if (code === 'delete') {
    onDelete(row);
  }
};

const scenicList = ref<any[]>([]);

onMounted(() => {
  getAllScenicList({}).then((res: any) => {
    scenicList.value = res.map((item: any) => ({
      label: item.scenicName,
      value: item.id,
    }));
  });
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
     collapsedRows: 1,
    wrapperClass: 'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: false,
  },

  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const res: any = await getTicketTypeList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});
</script>

<template>
  <Page auto-content-height>
    <FormModel :scenicList="scenicList" @success="onRefresh" />
    <Grid>
      <template #form-scenicId="slotProps">
        <Select
          v-bind="slotProps"
          placeholder="请选择所属景区"
          allow-clear
          :options="scenicList"
        />
      </template>
      <template #toolbar-actions>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          新增门票分类
        </Button>
      </template>
    </Grid>
  </Page>
</template>
