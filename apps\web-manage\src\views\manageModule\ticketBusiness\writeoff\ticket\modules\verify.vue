<script setup lang="ts">
import { ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { getVerificationInfo } from '#/api/manageModule';
import { Divider, Image, InputNumber } from 'ant-design-vue';

const [Modal, modalApi] = useVbenModal({
  confirmText: '确认核销',
  async onConfirm() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    const data = modalApi.getData<any>();
    if (isOpen) {
      params.value = { verificationCode: data.verificationCode };
      getLogList();
    }
  },
});
const params = ref<any>({
  verificationCode: '',
});
const orderInfo = ref<any>([]);
const getLogList = async () => {
  const res = await getVerificationInfo(params.value);
  orderInfo.value = res;
};

const num = ref(1);

const selectData = ref<any>({});
const selectTicket = (data: any) => {
  console.log(data);
  selectData.value = data;
};
</script>
<template>
  <Modal class="w-[1000px]" title="核销">
    <div class="flex justify-between gap-5">
      <div class="flex-1">
        <h3 class="mb-3 text-[16px] font-bold">订单信息</h3>
        <div
          class="mb-5 rounded-[5px] bg-[#f5f5f5] p-3 text-[14px] text-[#333]"
        >
          <p class="leading-[20px]">
            核销码：{{ orderInfo.orderDetail?.verificationCode }}
          </p>
          <p class="leading-[20px]">
            有效期：{{
              orderInfo.orderItemInfo?.validType == 1
                ? orderInfo.orderItemInfo?.validBeginDate + '当天有效'
                : orderInfo.orderItemInfo?.validBeginDate
                  ? orderInfo.orderItemInfo?.validBeginDate +
                    '至' +
                    orderInfo.orderItemInfo?.validEndDate
                  : ''
            }}
          </p>
        </div>
        <div class="flex">
          <div class="flex-shrink-0 rounded-md shadow-sm">
            <Image
              :src="orderInfo.orderItemInfo?.ticketCover"
              width="150px"
              height="150px"
              alt=""
            />
          </div>
          <div class="ml-4 text-[14px] leading-6">
            <h3 class="mb-3 text-[16px] font-bold">
              {{ orderInfo.orderItemInfo?.ticketName }}
            </h3>
            <p>
              联系人：<span>{{ orderInfo.orderInfo?.userName }}</span>
              <span class="ml-2">{{ orderInfo.orderInfo?.userPhone }}</span>
            </p>
            <p>
              出行人：<span>{{ orderInfo.orderDetail?.touristName }}</span>
              <span class="ml-2">{{
                orderInfo.orderDetail?.touristPhone
              }}</span>
            </p>
            <p>
              包含子票：{{
                orderInfo.childOrderDetailList
                  ?.map((item: any) => item.ticketName)
                  .join('，')
              }}
            </p>
            <p v-if="orderInfo.orderDetail?.model == 4">
              总次数限制：{{
                orderInfo.orderDetail?.cardLimitUseNum > 0
                  ? orderInfo.orderDetail?.cardLimitUseNum + '次'
                  : '不限'
              }}
            </p>
          </div>
        </div>
        <div
          class="mt-5 flex justify-around"
          v-if="orderInfo.orderDetail?.model == 1"
        >
          <div class="text-center">
            <p class="text-[20px] font-bold leading-[1.8]">
              {{ orderInfo.orderDetail?.verificationTotal }}
            </p>
            <p class="text-[14px]">总核销次数</p>
          </div>
          <a-divider type="vertical" />
          <div class="text-center">
            <p class="text-[20px] font-bold leading-[1.8]">
              {{ orderInfo.orderDetail?.canVerificationNum }}
            </p>
            <p class="text-[14px]">剩余核销次数</p>
          </div>
        </div>
      </div>
      <!-- 普通门票 -->
      <div class="flex-1" v-if="orderInfo.orderDetail?.model == 1">
        <h3 class="mb-3 text-[16px] font-bold">输入核销次数</h3>
        <InputNumber
          v-model:value="num"
          :defaultValue="1"
          :min="1"
          :precision="0"
          :max="orderInfo.orderDetail?.canVerificationNum"
          placeholder="请输入核销次数"
          class="w-full"
        ></InputNumber>
      </div>
      <!-- 套票 -->
      <div class="flex-1" v-if="orderInfo.orderDetail?.model == 2">
        <h3 class="mb-3 text-[16px] font-bold">选择门票核销</h3>
        <div class="grid grid-cols-2 gap-3">
          <div
            v-for="(item, index) in orderInfo.childOrderDetailList"
            :key="index"
            class="box-border cursor-pointer rounded-lg bg-[#f5f6f9] p-3 text-[14px] leading-[20px]"
            :class="
              selectData.id === item.id
                ? 'border-[2px] border-[#1a73e8]'
                : 'border-[2px] border-[#f5f6f9]'
            "
            @click="selectTicket(item)"
          >
            <p class="mb-2 font-bold">{{ item.ticketName }}</p>
            <p class="mb-1">可核销次数：{{ item.verificationTotal }}</p>
            <p>剩余可核销：{{ item.canVerificationNum }}</p>
          </div>
        </div>
      </div>
      <!-- 时效卡，次数卡，畅游卡 -->
      <div
        class="flex-2"
        v-if="[3, 4, 5].includes(orderInfo.orderDetail?.model)"
      >
        <h3 class="mb-3 text-[16px] font-bold">选择门票核销</h3>
        <div class="grid grid-cols-2 gap-3">
          <div
            v-for="(item, index) in orderInfo.childOrderDetailList"
            :key="index"
            class="box-border cursor-pointer rounded-lg bg-[#f5f6f9] p-3 text-[14px] leading-[20px]"
            :class="
              selectData.id === item.id
                ? 'border-[2px] border-[#1a73e8]'
                : 'border-[2px] border-[#f5f6f9]'
            "
            @click="selectTicket(item)"
          >
            <p class="mb-2 font-bold">{{ item.ticketName }}</p>
            <p v-if="item.isLimit == 0">限制：不限次数</p>
            <p v-else>
              限制：共{{ item.totalLimit }}次，每月{{
                item.monthLimit
              }}次，每日{{ item.dayLimit }}次
            </p>
            <p class="mt-1" v-if="item.isLimit == 1">
              核销：总{{ item.totalVerifyCount }}次，当月{{
                item.monthVerifyCount
              }}次，当日{{ item.todayVerifyCount }}次
            </p>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>
<style lang="scss" scoped></style>
