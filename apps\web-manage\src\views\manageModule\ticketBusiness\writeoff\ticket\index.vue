<script setup lang="ts">
import { Page } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import { useColumns, useGridFormSchema } from './data';
import { getOrderList } from '#/api/manageModule';
import { useAccessStore } from '@vben/stores';
import { toRefs, ref } from 'vue';
const { accessAllEnums } = toRefs(useAccessStore());

import { Table, Button } from 'ant-design-vue';
import type { TableColumnType } from 'ant-design-vue';
import { router } from '#/router';

const onActionClick = ({ code, row }: { code: string; row: any }) => {
  console.log(code, row);
};

const handleInfo = (row: any) => {
  console.log(row, '789');
  router.push({
    name: 'ticketWriteoffDetail',
    query: { id: row.id },
  });
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['orderDate', ['orderStartDate', 'orderEndDate']]],
    schema: useGridFormSchema(),
    collapsed: true,
    submitOnChange: true,
    submitOnEnter: true,
    collapsedRows: 1,
    wrapperClass:
      'grid-cols-4 md:grid-cols-2 xl:grid-cols-4 2xl:grid-cols-5 gap-2',
    showCollapseButton: true,
  },
  gridEvents: {
    // pageChange: ({ currentPage }: any) => {
    //   setAllExpand(true);
    // },
  },
  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    border: 'inner',
    pagerConfig: {
      pageSize: 10,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }: any, formValues: any) => {
          const res: any = await getOrderList({
            page: page.currentPage,
            pageSize: page.pageSize,
            payStatus: 2,
            ...formValues,
          });
          //循环数据
          res.list.forEach((item: any) => {
            item.isParent = true;
            if (item.orderItemList && item.orderItemList.length) {
              item.orderItemList.forEach((item2: any, index: number) => {
                item2.isParent = false;
                item2.orderId = item.id; // 添加订单ID引用
                item2.itemIndex = index; // 添加项目索引
                item2.orderTime = item.orderTime;
                item2.orderPrice = item.orderPrice;
                item2.discountPrice = item.discountPrice;
                item2.actualPrice = item.actualPrice;
                item2.orderType = item.orderType;
                item2.payStatus = item.payStatus;
                item2.orderStatus = item.orderStatus;
                item2.payMethod = item.payMethod;
                item2.orderSource = item.orderSource;
              });
            }
          });
          setAllExpand(true);
          orderList.value = res.list;
          return {
            items: res.list,
            total: res.total,
          };
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    virtualXConfig: {
      enabled: false,
    },
    virtualYConfig: {
      enabled: false,
    },
    spanMethod({ row, column }) {
      if (row.orderItemList && row.orderItemList.length) {
        if (column.field === 'ticketName') {
          return { rowspan: 1, colspan: 11 };
        }
      }
      return { rowspan: 1, colspan: 1 };
    },
    expandConfig: {
      mode: 'inside',
      expandAll: true,
      reserve: true,
    },
    treeConfig: {
      rowField: 'id',
      parentField: 'orderId',
      childrenField: 'orderItemList',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<any>,
});

const setAllExpand = (value: any) => {
  setTimeout(() => {
    gridApi.grid.setAllRowExpand(value);
  }, 50);
  // setTimeout(() => {
  //   gridApi.grid.setRowExpand(orderList.value, true);
  // }, 50);
};
const filterSource = (orderSource: any) => {
  return accessAllEnums.value?.ticketOrderSource.list.find(
    (item: any) => item.value === orderSource,
  )?.label;
};
const filterModel = (model: any) => {
  return accessAllEnums.value?.ticketModel.list.find(
    (item: any) => item.value === model,
  )?.label;
};

// 展开表格
const orderList = ref<any[]>([]);

// 需要合并单元格的字段
const mergeFields = [
  'orderTime',
  'orderPrice',
  'discountPrice',
  'actualPrice',
  'payStatus',
  'orderStatus',
  'payMethod',
  'options',
];

// 单元格合并函数
const getMergeProps = (
  field: string,
  index: number | undefined,
  dataSource: any[],
) => {
  // 只有需要合并的字段才进行合并处理
  if (!mergeFields.includes(field) || index === undefined) {
    return {};
  }

  // 如果是第一行，则显示合并的单元格
  if (index === 0) {
    return {
      rowSpan: dataSource.length,
    };
  }

  // 其他行不显示
  return {
    rowSpan: 0,
  };
};

const columns: TableColumnType[] = [
  {
    title: '门票信息',
    dataIndex: 'ticketName',
    fixed: 'left',
    minWidth: 400,
    customRender: ({ record }: any) => {
      return (
        record.ticketName + '（' + filterModel(record.model) + '）' || '--'
      );
    },
  },
  {
    title: '单价',
    dataIndex: 'unitPrice',
    width: 120,
    align: 'center',
  },
  {
    title: '数量',
    dataIndex: 'ticketNum',
    width: 120,
    align: 'center',
  },
  
  {
    title: '订单金额',
    dataIndex: 'orderPrice',
    width: 120,
    align: 'center',
    customCell: (record: any) => {
      const currentOrder = orderList.value.find(
        (item) => item.id === record.orderId,
      );
      return getMergeProps(
        'orderPrice',
        record.itemIndex,
        currentOrder?.orderItemList || [],
      );
    },
  },
  {
    title: '优惠金额',
    dataIndex: 'discountPrice',
    width: 120,
    align: 'center',
    customCell: (record: any) => {
      const currentOrder = orderList.value.find(
        (item) => item.id === record.orderId,
      );
      return getMergeProps(
        'discountPrice',
        record.itemIndex,
        currentOrder?.orderItemList || [],
      );
    },
  },
  {
    title: '实际支付金额',
    dataIndex: 'actualPrice',
    width: 120,
    align: 'center',
    customCell: (record: any) => {
      const currentOrder = orderList.value.find(
        (item) => item.id === record.orderId,
      );
      return getMergeProps(
        'actualPrice',
        record.itemIndex,
        currentOrder?.orderItemList || [],
      );
    },
  },
  {
    title: '支付状态',
    dataIndex: 'payStatus',
    width: 180,
    align: 'center',
    customCell: (record: any) => {
      const currentOrder = orderList.value.find(
        (item) => item.id === record.orderId,
      );
      return getMergeProps(
        'payStatus',
        record.itemIndex,
        currentOrder?.orderItemList || [],
      );
    },
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.orderPayStatus.list.find(
        (item: any) => item.value === record.payStatus,
      )?.label;
    },
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    width: 180,
    align: 'center',
    customCell: (record: any) => {
      const currentOrder = orderList.value.find(
        (item) => item.id === record.orderId,
      );
      return getMergeProps(
        'orderStatus',
        record.itemIndex,
        currentOrder?.orderItemList || [],
      );
    },
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.ticketOrderStatus.list.find(
        (item: any) => item.value === record.orderStatus,
      )?.label;
    },
  },
  {
    title: '支付方式',
    dataIndex: 'payMethod',
    width: 180,
    align: 'center',
    customCell: (record: any) => {
      const currentOrder = orderList.value.find(
        (item) => item.id === record.orderId,
      );
      return getMergeProps(
        'payMethod',
        record.itemIndex,
        currentOrder?.orderItemList || [],
      );
    },
    customRender: ({ record }: any) => {
      return accessAllEnums.value?.orderPayMethod.list.find(
        (item: any) => item.value === record.payMethod,
      )?.label;
    },
  },
  {
    title: '下单时间',
    dataIndex: 'orderTime',
    width: 180,
    align: 'center',
    customCell: (record: any) => {
      // 使用记录中的 itemIndex 而不是表格的 index
      const currentOrder = orderList.value.find(
        (item) => item.id === record.orderId,
      );
      return getMergeProps(
        'orderTime',
        record.itemIndex,
        currentOrder?.orderItemList || [],
      );
    },
  },
  {
    title: '',
    dataIndex: 'options',
    width: 120,
    align: 'center',
    customCell: (record: any) => {
      const currentOrder = orderList.value.find(
        (item) => item.id === record.orderId,
      );
      return getMergeProps(
        'options',
        record.itemIndex,
        currentOrder?.orderItemList || [],
      );
    },
  },
];
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #expand_header="{ row }">
        <div v-if="row.isParent" class="flex items-center justify-between">
          <div>
            <span>订单号：{{ row.orderNo }}</span>
            <span class="ml-5"
              >订单来源：{{ filterSource(row.orderSource) }}</span
            >
          </div>
          <div class="flex w-[105px] justify-center gap-2">
            <Button type="link" size="small" @click="handleInfo(row)"
              >详情</Button
            >
          </div>
        </div>
        <div v-else>{{ row.ticketName }}</div>
      </template>
      <template #expand_content="{ row }">
        <Table
          :key="row.id"
          :columns="columns"
          :data-source="row.orderItemList"
          :pagination="false"
          :show-header="false"
          :scroll="{ x: true }"
          :row-key="(record: any) => record.id || record.ticketId"
          :bordered="true"
          class="expand-table"
        ></Table>
      </template>
    </Grid>
  </Page>
</template>

<style scoped>
.expand-table {
  margin: 8px 0px;
}

.expand-table :deep(.ant-table-tbody > tr > td) {
  padding: 8px 16px;
}
.expand-table :deep(.ant-table-container) {
  border-start-start-radius: 0px !important;
  border-start-end-radius: 0px !important;
}
.expand-table :deep(.ant-table-container table) {
  border-radius: 0px !important;
}
.expand-table :deep(.ant-table-tbody > tr > td:last-child[rowspan]) {
  border-right: none !important;
}
</style>
