<script setup lang="ts">
import { ref, onMounted, toRefs } from 'vue';
import { Page, useVbenModal } from '@vben/common-ui';
import {
  PageHeader,
  Card,
  Button,
  Descriptions,
  DescriptionsItem,
  Table,
} from 'ant-design-vue';
import {
  getCustomizeRefundInfo,
  getCustomizeRefundRefundLog,
} from '#/api/manageModule';
import { useRoute } from 'vue-router';
import { useAccessStore } from '@vben/stores';
const { accessAllEnums } = toRefs(useAccessStore());

import { useLogTableSchema } from './data';

const route = useRoute();
const orderId = ref<any>(route.query.id);
const refundInfo = ref<any>({});
const getOrderInfoData = async () => {
  const res = await getCustomizeRefundInfo(orderId.value);
  refundInfo.value = res;
};
const refundLog = ref([]);
const total = ref(0);
const params = ref({
  refundId: orderId.value,
  page: 1,
  pageSize: 10,
});
const getCustomizeRefundRefundLogList = async () => {
  const res = await getCustomizeRefundRefundLog(params.value);
  refundLog.value = res.list;
  total.value = res.total;
};
onMounted(() => {
  getOrderInfoData();
  getCustomizeRefundRefundLogList();
});
const filterText = (arr: any[], val: any) => {
  return arr.find((item: any) => item.value === val)?.label;
};
</script>
<template>
  <Page>
    <div class="bg-card">
      <PageHeader
        title="自定义收款单退款详情"
        class="p-3"
        @back="() => $router.back()"
      ></PageHeader>
      <div class="px-3 pb-3">
        <Card title="退单信息" class="mb-5">
          <Descriptions>
            <DescriptionsItem label="订单号">{{
              refundInfo.orderInfo?.orderNo
            }}</DescriptionsItem>
            <DescriptionsItem label="退单号">{{
              refundInfo.refundNo
            }}</DescriptionsItem>
            <DescriptionsItem label="退款金额">
              {{ refundInfo.refundPrice }}
            </DescriptionsItem>
            <DescriptionsItem label="退款续费">
              {{ refundInfo.refundFeePrice }}
            </DescriptionsItem>
            <DescriptionsItem label="申请时间">{{
              refundInfo.applyTime
            }}</DescriptionsItem>
            <DescriptionsItem label="退款时间" v-if="refundInfo.refundTime">{{
              refundInfo.refundTime
            }}</DescriptionsItem>
            <DescriptionsItem label="退款原因">
              {{ refundInfo.refundReason }}
            </DescriptionsItem>
            <DescriptionsItem label="退款状态">{{
              filterText(
                accessAllEnums?.orderRefundStatus.list,
                refundInfo.refundStatus,
              )
            }}</DescriptionsItem>
            <DescriptionsItem label="操作员">
              {{ refundInfo.adminUserInfo?.name }}
              <span class="ml-2" v-if="refundInfo.adminUserInfo?.phone">{{
                refundInfo.adminUserInfo?.phone
              }}</span>
            </DescriptionsItem>
            <DescriptionsItem label="备注">
              {{ refundInfo.remark }}
            </DescriptionsItem>
          </Descriptions>
        </Card>
        <Card title="订单日志">
          <Table
            :columns="useLogTableSchema()"
            :dataSource="refundLog"
            :pagination="{
              current: params.page,
              pageSize: params.pageSize,
              total: total,
              onChange: (page, pageSize) => {
                params.page = page;
                params.pageSize = pageSize;
                getCustomizeRefundRefundLogList();
              },
              showTotal: (total) => `共 ${total} 条`,
            }"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex == 'ticketName'">
                <p v-if="record.adminUserInfo">
                  {{ record.adminUserInfo?.name }}<br />{{
                    record.adminUserInfo?.phone
                  }}
                </p>
                <p v-else>--</p>
              </template>
            </template>
          </Table>
        </Card>
      </div>
    </div>
  </Page>
</template>
